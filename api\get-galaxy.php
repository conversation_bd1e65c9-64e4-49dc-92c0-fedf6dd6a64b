<?php
/**
 * API endpoint để lấy thông tin galaxy
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: ' . CORS_ORIGIN);
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Chỉ cho phép GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Include các file cần thiết
    require_once __DIR__ . '/../config/database.php';
    require_once __DIR__ . '/../classes/Galaxy.php';
    
    // Lấy galaxy ID từ URL parameter
    $galaxyId = $_GET['id'] ?? '';
    
    if (empty($galaxyId)) {
        throw new Exception('Galaxy ID is required');
    }
    
    // Validate galaxy ID format
    if (!preg_match('/^[a-zA-Z0-9]{' . GALAXY_ID_LENGTH . '}$/', $galaxyId)) {
        throw new Exception('Invalid galaxy ID format');
    }
    
    // Lấy thông tin galaxy
    $galaxy = new Galaxy();
    $data = $galaxy->getGalaxyById($galaxyId);
    
    if ($data === null) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'Galaxy not found'
        ]);
    } else {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $data
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
