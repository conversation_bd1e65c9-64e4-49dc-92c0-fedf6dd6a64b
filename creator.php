<?php
// Include config để có thể sử dụng các constants nếu cần
require_once __DIR__ . '/config/database.php';
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON><PERSON> hạnh phúc</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            font-family: 'Orbitron', sans-serif;
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        
        h1 {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        #qrCode {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
}

#qrCode canvas {
    border-radius: 15px;
}
        
        .form-container {
            background: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .form-group {
            margin-bottom: 2rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 700;
            color: #4ecdc4;
        }
        
        .form-group textarea,
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(78, 205, 196, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: rgb(18, 168, 192);
            font-family: 'Orbitron', sans-serif;
            font-size: 1rem;
        }
        
        .form-group textarea {
            height: 150px;
            resize: vertical;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
        }
        
        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .color-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
        }
        
        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .icon-selector {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .icon-option {
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.05);
            cursor: pointer;
            text-align: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .icon-option:hover,
        .icon-option.selected {
            border-color: #4ecdc4;
            background: rgba(78, 205, 196, 0.2);
            transform: scale(1.1);
        }
        
        .btn {
            width: 100%;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 700;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            color: white;
            font-family: 'Orbitron', sans-serif;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 107, 157, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #4ecdc4;
            text-decoration: none;
            font-weight: 700;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            color: white;
            transform: translateX(-5px);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result {
            display: none;
            text-align: center;
            padding: 2rem;
            background: rgba(78, 205, 196, 0.1);
            border-radius: 15px;
            border: 2px solid #4ecdc4;
            margin-top: 2rem;
        }
        
        .result-link {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            word-break: break-all;
            font-family: monospace;
        }
        
        .copy-btn {
            background: transparent;
            border: 2px solid #4ecdc4;
            color: #4ecdc4;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Orbitron', sans-serif;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: rgba(78, 205, 196, 0.2);
        }
        
        .error-message {
            background: #ff4757;
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 10px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .form-container {
                padding: 1.5rem;
            }
            
            .icon-selector {
                grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="back-btn">
            ← Về trang chủ
        </a>
        
        <div class="header">
            <h1>Gửi ngàn yêu thương</h1>
            <p>
                hãy gửi những lời yêu thương của bạn vào vũ trụ,
            </p>
        </div>

        <div class="form-container">
            <form id="galaxyForm">
                <div class="form-group">
                    <label for="messages">Tin nhắn của bạn (mỗi dòng một tin nhắn):</label>
                    <textarea
                        id="messages"
                        placeholder="Happy Birthday&#10;I love you&#10;Chúc mừng sinh nhật&#10;Yêu em nhiều lắm&#10;càng nhiều sẽ càng đẹp nhé..."
                        required
                    ></textarea>
                    <small style="opacity: 0.7;">Mỗi dòng sẽ là một tin nhắn bay trong vũ trụ</small>
                </div>

                <div class="form-group">
                    <label>Chọn biểu tượng muốn hiển thị:</label>
                    <div class="icon-selector" id="iconSelector">
                        <div class="icon-option selected" data-icons='["♥", "💖", "💕", "💗"]'>♥💖💕💗</div>
                        <div class="icon-option" data-icons='["🎉", "🎂", "🎈", "🎊"]'>🎉🎂🎈🎊</div>
                        <div class="icon-option" data-icons='["🌟", "✨", "⭐", "🌠"]'>🌟✨ ⭐🌠</div>
                        <div class="icon-option" data-icons='["🌸", "🌺", "🌻", "🌹"]'>🌸🌺🌻🌹</div>
                        <div class="icon-option" data-icons='["🦋", "🐝", "🌈", "☀️"]'>🦋🐝🌈☀️</div>
                        <div class="icon-option" data-icons='["😊", "😍", "🥰", "😘"]'>😊😍🥰😘</div>
                    </div>
                    <input type="text" id="customIcons" placeholder="Hoặc nhập icon riêng, cách nhau bởi dấu phẩy (VD: 🐱,🐶,🦄)" style="margin-top:1rem;width:100%;padding:0.5rem;">
                </div>

                <div class="form-group">
                    <label>Màu sắc cho các loại tin nhắn:</label>
                    <div class="color-picker-group">
                        <div class="color-option">
                            <div class="color-preview" style="background: #ff6b9d;"></div>
                            <label>Tin nhắn tình yêu:</label>
                            <input type="color" id="loveColor" value="#ff6b9d">
                        </div>
                        <div class="color-option">
                            <div class="color-preview" style="background: #ff6b9d;"></div>
                            <label>Sinh nhật:</label>
                            <input type="color" id="birthdayColor" value="#ff6b9d">
                        </div>
                        <div class="color-option">
                            <div class="color-preview" style="background: #ff6b9d;"></div>
                            <label>Ngày tháng:</label>
                            <input type="color" id="dateColor" value="#ff6b9d">
                        </div>
                        <div class="color-option">
                            <div class="color-preview" style="background: #ff6b9d;"></div>
                            <label>Tin nhắn khác:</label>
                            <input type="color" id="specialColor" value="#ff6b9d">
                        </div>
                        <div class="color-option">
                            <div class="color-preview" style="background: #ff6b9d;"></div>
                            <label>Biểu tượng:</label>
                            <input type="color" id="heartColor" value="#ff6b9d">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="song">Chọn bài hát (nếu muốn):</label>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <select id="song">
                            <option value="" selected>-- Chọn một bài hát có sẵn --</option>
                            <option value="happy-birthday.mp3">Happy Birthday</option>
                            <option value="happybirtday_uia.mp3">Happy Birthday Mèo U I A</option>
                            <option value="happybirthday_domixi.mp3">Happy Birthday Độ Mixi</option>
                            <option value="eyenoselip.mp3">Eyes, Nose, Lips</option>
                            <option value="anhnangcuaanh.mp3">Anh nắng của anh</option>
                            <option value="suynghitronganh.mp3">Suy nghĩ trong anh</option>
                            <option value="yeuemhonmoingay.mp3">Yêu em hơn mỗi ngày</option>
                            <option value="phepmau.mp3">Phép màu</option>
                            <!-- Thêm các bài hát khác ở đây -->
                        </select>
                        <button type="button" id="previewDefaultSongBtn" title="Nghe thử bài hát mặc định"
                        style ="width: 30px;height: 30px ; background: transparent; border: none; cursor: pointer; color: #4ecdc4;">
                            🔊</button>
                    </div>
                    <div style="margin: 1rem 0; text-align:center;">— Hoặc —</div>
                    <input type="url" id="customSongUrl" placeholder="Nhập link file nhạc (.mp3, .ogg, ...)" style="width:100%;margin-bottom:0.5rem;">
                    <button type="button" id="checkSongBtn" style="margin-bottom:0.5rem;">🔊 Check Link Nhạc</button>
                    <div style="margin: 1rem 0; text-align:center;">— Hoặc Upload File —</div>
                    <input type="file" id="songFile" accept="audio/*" style="width:100%;margin-bottom:0.5rem;">
                    <audio id="customSongAudio" controls style="display:none;width:100%;margin-top:0.5rem;"></audio>
                </div>

                <div class="form-group">
                    <label for="images">Chọn ảnh (tối đa 5 ảnh, PNG/JPG/GIF):</label>
                    <input type="file" id="images" accept="image/*" multiple style="width:100%;margin-bottom:0.5rem;">
                    <div id="imagePreview" style="display:flex;gap:10px;margin-top:10px;"></div>
                </div>

                <button type="submit" class="btn" id="submitBtn">
                    ✨ Tạo yêu thương
                </button>
            </form>

            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <p>Đang tạo galaxy của bạn...</p>
            </div>

            <div class="result" id="result">
                <h3>🎉 Galaxy của bạn đã sẵn sàng!</h3>
                <p>Chia sẻ link này với bạn bè:</p>
                <div class="result-link" id="resultLink"></div>
                <button class="copy-btn" id="copyBtn">📋 Copy Link</button>
                <br><br>
                <a href="#" id="viewBtn" class="btn" target="_blank">🌌 Xem kết quả</a>
                <div id="qrCode" style="margin-top: 4rem;"></div>
                <div id="mes" style="margin-top: 4rem;"> <p>
                    Hãy gửi những lời yêu thương này tới người bạn yêu quý nhé! chúc bạn có một ngày thật tuyệt vời. Mình là Trí Toán
                    hãy liên hệ với mình qua <a href="https://www.facebook.com/iamtritoan/" target="_blank"
                        style="color: #fc5919; text-decoration: none;"
                    >Facebook</a> nếu bạn cần giúp đỡ hoặc có ý tưởng gì mới nhé!
                </p></div>

            </div>

        </div>

    </div>

    <script src="https://cdn.jsdelivr.net/npm/qr-code-styling@1.5.0/lib/qr-code-styling.js"></script>
    <script src="js/galaxy-api.js"></script>
    <script>
    // Hàm vẽ pattern trái tim xung quanh QR
    function drawHeartPattern(canvas, size) {
        const ctx = canvas.getContext('2d');
        canvas.width = size;
        canvas.height = size;

        // Clear canvas
        ctx.clearRect(0, 0, size, size);

        // Tạo pattern nhỏ giống QR code
        const dotSize = 2;
        const gap = 1;
        const step = dotSize + gap;

        // Tạo nhiều lớp trái tim với kích thước khác nhau
        for (let layer = 0; layer < 3; layer++) {
            const scale = 0.8 + layer * 0.4;

            for (let x = 0; x < size; x += step) {
                for (let y = 0; y < size; y += step) {
                    const centerX = size / 2;
                    const centerY = size / 2;
                    const relX = (x - centerX) / (size * 0.3 * scale);
                    const relY = (y - centerY) / (size * 0.3 * scale);

                    // Công thức hình trái tim
                    const heartEq = Math.pow(relX * relX + relY * relY - 1, 3) - relX * relX * Math.pow(relY, 3);

                    const distFromCenter = Math.sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    const isInHeartShape = heartEq <= 0;
                    const isNotInCenter = distFromCenter > size * 0.2;

                    if (isInHeartShape && isNotInCenter) {
                        const opacity = (0.15 + layer * 0.05) * (distFromCenter / (size * 0.4));
                        const colors = ['#ff6b9d', '#4ecdc4', '#ffd93d'];
                        ctx.fillStyle = colors[layer] + Math.floor(opacity * 255).toString(16).padStart(2, '0');

                        if (Math.random() > 0.4 - layer * 0.1) {
                            ctx.fillRect(x, y, dotSize, dotSize);
                        }
                    }
                }
            }
        }
    }

    // Hàm tạo QR với kiểm tra thư viện
    async function createHeartQR(galaxyUrl) {
        // Kiểm tra xem QRCodeStyling đã load chưa
        if (typeof QRCodeStyling === 'undefined') {
            console.error('QRCodeStyling chưa được load!');
            // Fallback: tạo QR đơn giản
            return createSimpleQR(galaxyUrl);
        }

        const qrDiv = document.getElementById('qrCode');
        qrDiv.innerHTML = "";

        // Tạo container
        const qrContainer = document.createElement('div');
        qrContainer.style.position = 'relative';
        qrContainer.style.display = 'inline-block';
        qrContainer.style.background = 'white';
        qrContainer.style.padding = '20px';
        qrContainer.style.borderRadius = '25px';
        qrContainer.style.boxShadow = '0 10px 30px rgba(0,0,0,0.3)';

        // Canvas cho pattern trái tim
        const heartCanvas = document.createElement('canvas');
        heartCanvas.style.position = 'absolute';
        heartCanvas.style.top = '0';
        heartCanvas.style.left = '0';
        heartCanvas.style.zIndex = '1';
        heartCanvas.style.pointerEvents = 'none';

        // Div cho QR code chính
        const qrCodeDiv = document.createElement('div');
        qrCodeDiv.style.position = 'relative';
        qrCodeDiv.style.zIndex = '2';

        try {
            const qrCode = new QRCodeStyling({
                width: 200,
                height: 200,
                data: galaxyUrl,
                image: "https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/72x72/2764.png",
                dotsOptions: {
                    color: "#ff6b9d",
                    type: "rounded"
                },
                backgroundOptions: {
                    color: "rgba(255, 255, 255, 0.9)"
                },
                imageOptions: {
                    crossOrigin: "anonymous",
                    margin: 6,
                    imageSize: 0.2
                },
                cornersSquareOptions: {
                    color: "#ff6b9d",
                    type: "extra-rounded"
                },
                cornersDotOptions: {
                    color: "#ff6b9d",
                    type: "dot"
                }
            });

            // Vẽ pattern trái tim
            drawHeartPattern(heartCanvas, 240);

            // Ghép các thành phần
            qrContainer.appendChild(heartCanvas);
            qrContainer.appendChild(qrCodeDiv);
            qrDiv.appendChild(qrContainer);

            // Append QR code
            qrCode.append(qrCodeDiv);

        } catch (error) {
            console.error('Lỗi tạo QR fancy:', error);
            // Fallback
            createSimpleQR(galaxyUrl);
        }
    }

    // Hàm tạo QR đơn giản khi lỗi
    function createSimpleQR(galaxyUrl) {
        const qrDiv = document.getElementById('qrCode');
        qrDiv.innerHTML = `
            <div style="padding: 20px; background: white; border-radius: 15px; color: #333;">
                <p><strong>Link Galaxy của bạn:</strong></p>
                <div style="word-break: break-all; font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 5px; margin: 10px 0;">
                    ${galaxyUrl}
                </div>
                <p><em>QR Code sẽ hiển thị sau khi thư viện load xong</em></p>
            </div>
        `;

        // Thử tạo lại sau 2 giây
        setTimeout(() => {
            if (typeof QRCodeStyling !== 'undefined') {
                createHeartQR(galaxyUrl);
            }
        }, 2000);
    }

    // === PHẦN CODE XỬ LÝ FORM ===

    // Xử lý chọn icon
    const iconOptions = document.querySelectorAll('.icon-option');
    let selectedIcons = ["♥", "💖", "💕", "💗"];
    const customIconsInput = document.getElementById('customIcons');

    iconOptions.forEach(option => {
        option.addEventListener('click', () => {
            iconOptions.forEach(opt => opt.classList.remove('selected'));
            option.classList.add('selected');
            selectedIcons = JSON.parse(option.dataset.icons);
            customIconsInput.value = "";
        });
    });

    customIconsInput.addEventListener('input', () => {
        if (customIconsInput.value.trim() !== "") {
            iconOptions.forEach(opt => opt.classList.remove('selected'));
        }
    });

    // Xử lý color picker
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('change', (e) => {
            const preview = e.target.parentElement.querySelector('.color-preview');
            preview.style.background = e.target.value;
        });
    });

    const imagesInput = document.getElementById('images');
    const imagePreview = document.getElementById('imagePreview');

    // Xem trước ảnh
    imagesInput.addEventListener('change', () => {
        imagePreview.innerHTML = '';
        Array.from(imagesInput.files).slice(0, 5).forEach(file => {
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            img.style.width = '60px';
            img.style.height = '60px';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '10px';
            imagePreview.appendChild(img);
        });
    });

    // Xử lý form submit
    document.getElementById('galaxyForm').addEventListener('submit', async (e) => {
        e.preventDefault();

        const submitBtn = document.getElementById('submitBtn');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const api = new GalaxyAPI();

        // Show loading
        api.showLoading(submitBtn, 'Đang tạo...');
        loading.style.display = 'block';
        result.style.display = 'none';

        try {
            const messages = document.getElementById('messages').value
                .split('\n')
                .filter(msg => msg.trim() !== '')
                .map(msg => msg.trim());

            let icons = selectedIcons;
            if (customIconsInput.value.trim() !== "") {
                icons = customIconsInput.value.split(",").map(i => i.trim()).filter(i => i !== "");
            }

            // Upload ảnh trước
            let imageUrls = [];
            if (imagesInput.files.length > 0) {
                try {
                    console.log('Uploading', imagesInput.files.length, 'images...');
                    imageUrls = await api.uploadImages(imagesInput.files);
                    console.log('Upload successful, URLs:', imageUrls);
                } catch (uploadError) {
                    console.warn('Upload ảnh thất bại:', uploadError);
                    // Tiếp tục tạo galaxy mà không có ảnh
                }
            }

            // Xử lý bài hát
            let songValue = '';
            const songFile = document.getElementById('songFile');

            if (songFile.files.length > 0) {
                // Upload file bài hát
                try {
                    songValue = await api.uploadSong(songFile.files[0]);
                } catch (uploadError) {
                    console.warn('Upload bài hát thất bại:', uploadError);
                }
            } else if (customSongUrl.value.trim() !== '') {
                // Sử dụng URL
                songValue = customSongUrl.value.trim();
            } else if (songSelect.value) {
                // Sử dụng bài hát có sẵn
                songValue = songSelect.value;
            }

            const galaxyData = {
                messages: messages,
                icons: icons,
                colors: {
                    love: document.getElementById('loveColor').value,
                    birthday: document.getElementById('birthdayColor').value,
                    date: document.getElementById('dateColor').value,
                    special: document.getElementById('specialColor').value,
                    heart: document.getElementById('heartColor').value
                },
                song: songValue,
                images: imageUrls
            };

            console.log('Galaxy data to be sent:', galaxyData);

            // Validate dữ liệu
            const errors = api.validateGalaxyData(galaxyData);
            if (errors.length > 0) {
                throw new Error(errors.join(', '));
            }

            // Tạo galaxy
            const response = await api.createGalaxy(galaxyData);

            // Hiện kết quả
            loading.style.display = 'none';
            result.style.display = 'block';

            document.getElementById('resultLink').textContent = response.url;
            document.getElementById('viewBtn').href = response.url;

            // Xử lý copy link
            document.getElementById('copyBtn').addEventListener('click', () => {
                navigator.clipboard.writeText(response.url).then(() => {
                    const copyBtn = document.getElementById('copyBtn');
                    const originalText = copyBtn.textContent;
                    copyBtn.textContent = '✅ Đã copy!';
                    setTimeout(() => {
                        copyBtn.textContent = originalText;
                    }, 2000);
                });
            });

            // Tạo QR code với hiệu ứng trái tim
            await createHeartQR(response.url);

        } catch (error) {
            console.error('Lỗi khi tạo galaxy:', error);
            loading.style.display = 'none';
            api.showError(error.message, document.querySelector('.form-container'));
        }

        api.hideLoading(submitBtn, '✨ Tạo yêu thương');
    });

    // Xử lý bài hát
    const songSelect = document.getElementById('song');
    const customSongUrl = document.getElementById('customSongUrl');
    const songFile = document.getElementById('songFile');
    const checkSongBtn = document.getElementById('checkSongBtn');
    const customSongAudio = document.getElementById('customSongAudio');
    const previewDefaultSongBtn = document.getElementById('previewDefaultSongBtn');

    songSelect.addEventListener('change', () => {
        if (songSelect.value) {
            customSongUrl.value = '';
            songFile.value = '';
            customSongAudio.style.display = 'none';
            customSongAudio.pause();
        }
    });

    customSongUrl.addEventListener('input', () => {
        if (customSongUrl.value.trim() !== '') {
            songSelect.value = '';
            songFile.value = '';
        }
        customSongAudio.style.display = 'none';
        customSongAudio.pause();
    });

    songFile.addEventListener('change', () => {
        if (songFile.files.length > 0) {
            songSelect.value = '';
            customSongUrl.value = '';

            // Preview uploaded file
            const file = songFile.files[0];
            const url = URL.createObjectURL(file);
            customSongAudio.src = url;
            customSongAudio.style.display = 'block';
        }
        customSongAudio.pause();
    });

    checkSongBtn.addEventListener('click', () => {
        const url = customSongUrl.value.trim();
        if (!url) {
            alert('Vui lòng nhập link file nhạc!');
            return;
        }
        customSongAudio.src = url;
        customSongAudio.style.display = 'block';
        customSongAudio.play().catch(() => {
            alert('Không phát được file nhạc này. Hãy kiểm tra lại link!');
        });
    });

    previewDefaultSongBtn.addEventListener('click', () => {
        const selectedSong = songSelect.value;
        if (!selectedSong) {
            alert('Vui lòng chọn một bài hát có sẵn!');
            return;
        }
        customSongAudio.src = `songs/${selectedSong}`;
        customSongAudio.style.display = 'block';
        customSongAudio.play().catch(() => {
            alert('Không phát được file nhạc này!');
        });
    });
</script>
</body>
</html>
