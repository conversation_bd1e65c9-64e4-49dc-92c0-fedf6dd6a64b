<?php
/**
 * API endpoint để tạo galaxy mới
 */

// Bắt tất cả errors và chuyển thành JSON
error_reporting(E_ALL);
ini_set('display_errors', 0); // Tắt hiển thị error trực tiếp

// Set headers trước khi có bất kỳ output nào
header('Content-Type: application/json; charset=utf-8');

// Include config trước để có CORS_ORIGIN
try {
    require_once __DIR__ . '/../config/database.php';
    header('Access-Control-Allow-Origin: ' . CORS_ORIGIN);
} catch (Exception $e) {
    header('Access-Control-Allow-Origin: *');
}

header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Chỉ cho phép POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Include các file cần thiết (config đã được include ở trên)
    require_once __DIR__ . '/../classes/Galaxy.php';
    
    // Lấy dữ liệu từ request
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON data');
    }

    // Debug log
    error_log('Received galaxy data: ' . print_r($data, true));
    
    // Validate dữ liệu cơ bản
    if (empty($data['messages']) || !is_array($data['messages'])) {
        throw new Exception('Messages are required and must be an array');
    }
    
    // Tạo galaxy
    $galaxy = new Galaxy();
    $result = $galaxy->createGalaxy($data);
    
    if ($result['success']) {
        http_response_code(201);
        echo json_encode($result);
    } else {
        http_response_code(400);
        echo json_encode($result);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
