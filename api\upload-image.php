<?php
/**
 * API endpoint để upload ảnh
 */

// B<PERSON>t tất cả errors và chuyển thành JSON
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Set headers trước khi có bất kỳ output nào
header('Content-Type: application/json; charset=utf-8');

// Include config trước để có CORS_ORIGIN
try {
    require_once __DIR__ . '/../config/database.php';
    header('Access-Control-Allow-Origin: ' . CORS_ORIGIN);
} catch (Exception $e) {
    header('Access-Control-Allow-Origin: *');
}

header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Chỉ cho phép POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

try {
    // Include các file cần thiết (config đã được include ở trên)
    require_once __DIR__ . '/../classes/Galaxy.php';
    
    // Kiểm tra có file upload không
    if (empty($_FILES['images'])) {
        throw new Exception('No images uploaded');
    }
    
    // Tạo thư mục upload nếu chưa có
    if (!is_dir(UPLOAD_DIR)) {
        if (!mkdir(UPLOAD_DIR, 0755, true)) {
            throw new Exception('Cannot create upload directory');
        }
    }
    
    $uploadedUrls = [];
    $files = $_FILES['images'];
    
    // Xử lý multiple files
    if (is_array($files['name'])) {
        $fileCount = count($files['name']);

        for ($i = 0; $i < $fileCount && $i < MAX_IMAGES; $i++) {
            if ($files['error'][$i] === UPLOAD_ERR_OK) {
                $url = uploadSingleFile([
                    'name' => $files['name'][$i],
                    'tmp_name' => $files['tmp_name'][$i],
                    'size' => $files['size'][$i],
                    'type' => $files['type'][$i]
                ]);

                if ($url) {
                    $uploadedUrls[] = $url;
                }
            }
        }
    } else {
        // Single file
        if ($files['error'] === UPLOAD_ERR_OK) {
            $url = uploadSingleFile($files);
            if ($url) {
                $uploadedUrls[] = $url;
            }
        }
    }
    
    if (empty($uploadedUrls)) {
        throw new Exception('No files were uploaded successfully');
    }
    
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'urls' => $uploadedUrls
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Upload một file đơn
 */
function uploadSingleFile($file) {
    try {
        // Validate file size
        if ($file['size'] > MAX_FILE_SIZE) {
            throw new Exception('File size too large: ' . $file['name']);
        }
        
        // Validate file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, ALLOWED_EXTENSIONS)) {
            throw new Exception('Invalid file type: ' . $file['name']);
        }
        
        // Validate file type bằng mime type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);
        
        $allowedMimes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/webp'
        ];
        
        if (!in_array($mimeType, $allowedMimes)) {
            throw new Exception('Invalid file mime type: ' . $file['name']);
        }
        
        // Tạo tên file unique
        $filename = uniqid() . '_' . time() . '.' . $extension;
        $filepath = UPLOAD_DIR . $filename;
        
        // Move file
        if (!move_uploaded_file($file['tmp_name'], $filepath)) {
            throw new Exception('Failed to move uploaded file: ' . $file['name']);
        }
        
        // Trả về URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $basePath = dirname(dirname($_SERVER['REQUEST_URI'])); // Lùi 2 cấp từ /api/upload-image.php

        return $protocol . '://' . $host . $basePath . UPLOAD_URL . $filename;
        
    } catch (Exception $e) {
        error_log('Upload error: ' . $e->getMessage());
        return false;
    }
}
?>
