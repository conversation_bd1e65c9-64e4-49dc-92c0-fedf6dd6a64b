<?php
/**
 * Debug script để kiểm tra galaxy data
 */

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/classes/Galaxy.php';

// Lấy galaxy ID từ URL
$galaxyId = $_GET['id'] ?? '';

if (empty($galaxyId)) {
    echo "<h1>Debug Galaxy Data</h1>";
    echo "<p>Usage: debug-galaxy.php?id=GALAXY_ID</p>";
    
    // Hiển thị danh sách galaxy có sẵn
    try {
        $db = Database::getInstance();
        $galaxies = $db->select("SELECT id, created_at FROM galaxies ORDER BY created_at DESC LIMIT 10");
        
        if (!empty($galaxies)) {
            echo "<h2>Recent Galaxies:</h2>";
            echo "<ul>";
            foreach ($galaxies as $galaxy) {
                echo "<li><a href='?id={$galaxy['id']}'>{$galaxy['id']}</a> - {$galaxy['created_at']}</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p>Error: " . $e->getMessage() . "</p>";
    }
    exit;
}

try {
    $galaxy = new Galaxy();
    $data = $galaxy->getGalaxyById($galaxyId);
    
    if ($data === null) {
        echo "<h1>Galaxy Not Found</h1>";
        echo "<p>Galaxy ID: $galaxyId</p>";
        exit;
    }
    
    echo "<h1>Galaxy Debug: $galaxyId</h1>";
    
    echo "<h2>Raw Data:</h2>";
    echo "<pre>" . print_r($data, true) . "</pre>";
    
    echo "<h2>Messages (" . count($data['messages']) . "):</h2>";
    echo "<ul>";
    foreach ($data['messages'] as $message) {
        echo "<li>" . htmlspecialchars($message) . "</li>";
    }
    echo "</ul>";
    
    echo "<h2>Icons (" . count($data['icons']) . "):</h2>";
    echo "<ul>";
    foreach ($data['icons'] as $icon) {
        echo "<li>" . htmlspecialchars($icon) . "</li>";
    }
    echo "</ul>";
    
    echo "<h2>Images (" . count($data['images']) . "):</h2>";
    if (!empty($data['images'])) {
        echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
        foreach ($data['images'] as $imageUrl) {
            echo "<div>";
            echo "<img src='" . htmlspecialchars($imageUrl) . "' style='max-width: 200px; max-height: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed to load\"'>";
            echo "<br><small>" . htmlspecialchars($imageUrl) . "</small>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<p>No images</p>";
    }
    
    echo "<h2>Colors:</h2>";
    echo "<div style='display: flex; gap: 10px;'>";
    foreach ($data['colors'] as $type => $color) {
        echo "<div style='text-align: center;'>";
        echo "<div style='width: 50px; height: 50px; background: $color; border: 1px solid #000; margin: 0 auto;'></div>";
        echo "<small>$type<br>$color</small>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Song:</h2>";
    if (!empty($data['song'])) {
        echo "<p>" . htmlspecialchars($data['song']) . "</p>";
        if (file_exists("songs/" . $data['song'])) {
            echo "<audio controls><source src='songs/" . htmlspecialchars($data['song']) . "'></audio>";
        } else if (filter_var($data['song'], FILTER_VALIDATE_URL)) {
            echo "<audio controls><source src='" . htmlspecialchars($data['song']) . "'></audio>";
        } else {
            echo "<p style='color: red;'>Song file not found</p>";
        }
    } else {
        echo "<p>No song</p>";
    }
    
    echo "<h2>Actions:</h2>";
    echo "<a href='galaxy-viewer.php?id=$galaxyId' target='_blank'>View Galaxy</a>";
    
} catch (Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
