<?php
// Include config để có thể sử dụng các constants n<PERSON>u cần
require_once __DIR__ . '/config/database.php';
?>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0">
    <title><PERSON><PERSON><PERSON> Hà 3D - Galaxy Generator</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            position: fixed;
            overflow: hidden;
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            background-color: #000; 
        }

        body {
            background: radial-gradient(ellipse at center, #0a0a0a 0%, #000000 100%);
            font-family: 'Orbitron', 'Courier New', monospace;
            perspective: 1500px;
            cursor: grab;
            touch-action: none;
            -webkit-tap-highlight-color: transparent;
            background-color: #000;
        }

        body:active {
            cursor: grabbing;
        }

        .galaxy-container {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
            transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg) scale(1);
            will-change: transform;
        }

        .text-particle {
            position: absolute;
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            white-space: nowrap;
            text-shadow: 
                0 0 10px currentColor,
                0 0 20px currentColor,
                0 0 30px currentColor,
                2px 2px 4px rgba(0,0,0,0.8);
            transform-style: preserve-3d;
            pointer-events: none;
            letter-spacing: 1px;
            will-change: transform, opacity;
            backface-visibility: hidden;
            transform: translateZ(0);
        }

        .text-particle.love {
            color: #ff6b9d;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.birthday {
            color: #4ecdc4;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.date {
            color: #c534ed;
            text-shadow: 
                0 0 20px currentColor,
                0 0 30px currentColor,
                0 0 40px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.special {
            color: #f34bce;
            text-shadow: 
                0 0 15px currentColor,
                0 0 25px currentColor,
                0 0 35px currentColor,
                2px 2px 6px rgba(0,0,0,0.9);
        }

        .text-particle.heart {
            color: #ff69b4;
            text-shadow: 
                0 0 20px currentColor,
                0 0 30px currentColor,
                0 0 40px currentColor,
                3px 3px 8px rgba(0,0,0,0.9);
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 0 10px white;
            animation: twinkle 3s infinite;
            will-change: opacity;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-family: 'Orbitron', sans-serif;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #4ecdc4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 2rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            color: white;
            font-family: 'Orbitron', sans-serif;
            text-align: center;
            padding: 2rem;
        }

        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .back-home {
            margin-top: 2rem;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #ff6b9d, #4ecdc4);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            transition: all 0.3s ease;
        }

        .back-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(255, 107, 157, 0.4);
        }

        .image-particle {
            transition: opacity 0.3s;
            will-change: transform, opacity;
            z-index: 2;
        }

        /* Mobile specific styles */
        @media (max-width: 768px) {
            body {
                perspective: 1000px;
            }

            .galaxy-container {
                perspective: 1000px;
            }

            .audio-control {
                width: 45px;
                height: 45px;
                font-size: 1.3rem;
                top: 15px;
                right: 15px;
            }

            .star {
                width: 1.5px;
                height: 1.5px;
            }
        }

        @media (max-width: 480px) {
            .audio-control {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
                top: 10px;
                right: 10px;
            }

            .star {
                width: 1px;
                height: 1px;
            }
        }

        /* Landscape orientation on mobile */
        @media (max-height: 500px) and (orientation: landscape) {
            .text-particle {
                font-size: 10px;
            }

            .text-particle.heart {
                font-size: 12px;
            }
        }

        /* Nút điều khiển âm thanh */
        .audio-control {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.5rem;
            color: white;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .audio-control:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.1);
        }

        .audio-control.muted {
            background: rgba(255, 0, 0, 0.2);
            border-color: rgba(255, 0, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
        <h2>Những điều tốt đẹp đều cần thời gian...</h2>
        <p>Vui lòng đợi trong giây lát</p>
    </div>

    <div class="error-screen" id="errorScreen">
        <div class="error-icon">😢</div>
        <h2>Không tìm thấy website</h2>
        <p>Link này có thể đã hết hạn hoặc không tồn tại.</p>
        <a href="index.php" class="back-home">🏠 Về trang chủ</a>
    </div>

    <div class="galaxy-container" id="galaxy">
        <!-- Các text particles sẽ được tạo bằng JavaScript -->
    </div>

    <!-- Nút điều khiển âm thanh -->
    <div class="audio-control" id="audioControl" style="display:none;" title="Bật/Tắt nhạc">
        🔊
    </div>

    <audio id="galaxyAudio" controls style="display:none"></audio>

    <script src="js/galaxy-api.js"></script>
    <script>
        // Thay thế Firebase bằng API calls

        // Lấy galaxy ID từ URL
        const urlParams = new URLSearchParams(window.location.search);
        const galaxyId = urlParams.get('id');
        const isDemo = urlParams.get('demo') === '1';


        const demoGalaxyData = {
    messages: [
        "I love you so much! ❤️",
        "Our Anniverasry",
        "I love you 💖",
        "25/08/2004",
        "Thank you for being my sunshine ",
        "Thank you for being my everything 💕",
        "You are my universe ", 
        "There is no other",
        "You're amazing", 
        "You make my heart smile ", 
        "Love ya! 💖",
        "Honey bunch, you are my everything! ",



    ],
    icons: ["♥", "💖", "❤️", "❤️", "💕", "💕"],
    colors: {
        love: "#ff6b9d",
        birthday: "#4ecdc4",
        date: "#ff69b4",
        special: "#ff6b9d",
        heart: "#ff69b4"
    },
    images: ["https://firebasestorage.googleapis.com/v0/b/staynowapp1.appspot.com/o/galaxy-images%2Fz6654939498839_d275d5b163f80fd572ba1403f32746fa.jpg?alt=media&token=759411f6-2c26-43ac-b1c0-bf9e31752744"],
    song: "eyenoselip.mp3",
    createdAt: "2025-05-30T00:00:00.000Z"
};

        const loadingScreen = document.getElementById('loadingScreen');
        const errorScreen = document.getElementById('errorScreen');
        const galaxy = document.getElementById('galaxy');

        let galaxyData = null;
        let rotationX = 0;
        let rotationY = 0;
        let scale = 1;
        let isDragging = false;
        let lastMouseX = 0;
        let lastMouseY = 0;
        const activeParticles = new Set();

        // Responsive settings
        const isMobile = window.innerWidth <= 768;
        const isSmallMobile = window.innerWidth <= 480;
        // Tăng số lượng particle tối đa
        const maxParticles = isSmallMobile ? 150 : isMobile ? 200 : 300;
        // Responsive particle interval - chậm hơn để dễ theo dõi
        const particleInterval = isSmallMobile ? 150 : isMobile ? 180 : 200;
        const starCount = isSmallMobile ? 250 : isMobile ? 350 : 500;

        let particleSpeedMultiplier = isMobile ? 1.5 : 1.2; // Chậm hơn mặc định
        let isSlowMode = false;

        // Làm chậm khi click/chạm (toggle mode)
        const toggleSlowMode = () => {
            isSlowMode = !isSlowMode;
            particleSpeedMultiplier = isSlowMode ?
                (isMobile ? 3.0 : 2.5) : // Chậm nhiều khi bật slow mode
                (isMobile ? 1.5 : 1.2);  // Tốc độ bình thường đã chậm hơn

            // Visual feedback
            document.body.style.cursor = isSlowMode ? 'grab' : 'default';

            // Hiển thị thông báo tạm thời
            showSpeedNotification(isSlowMode);
        };

        // Thông báo tốc độ
        const showSpeedNotification = (isSlowMode) => {
            // Remove existing notification
            const existing = document.querySelector('.speed-notification');
            if (existing) existing.remove();

            const notification = document.createElement('div');
            notification.className = 'speed-notification';
            notification.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 1rem 2rem;
                border-radius: 25px;
                font-family: 'Orbitron', sans-serif;
                font-size: ${isMobile ? '14px' : '16px'};
                z-index: 10000;
                pointer-events: none;
                backdrop-filter: blur(10px);
                border: 2px solid ${isSlowMode ? '#4ecdc4' : '#ff6b9d'};
                animation: fadeInOut 2s ease-in-out;
            `;
            notification.textContent = isSlowMode ?
                '🐌 Chế độ chậm - Chạm lại để tăng tốc' :
                '⚡ Tốc độ bình thường - Chạm để làm chậm';

            document.body.appendChild(notification);

            // Auto remove
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 2000);
        };

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);

        // Event listeners cho toggle
        document.addEventListener('click', toggleSlowMode);
        document.addEventListener('touchstart', (e) => {
            // Chỉ toggle khi chạm đơn (không phải pinch zoom)
            if (e.touches.length === 1) {
                e.preventDefault();
                toggleSlowMode();
            }
        });

        // Prevent scrolling and zooming
        document.addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('wheel', function(e) {
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('gesturestart', function(e) {
            e.preventDefault();
        });

        document.addEventListener('gesturechange', function(e) {
            e.preventDefault();
        });

        document.addEventListener('gestureend', function(e) {
            e.preventDefault();
        });

        // Load galaxy data from API
        async function loadGalaxyData() {
            if (isDemo) {
                galaxyData = demoGalaxyData;
                initializeGalaxy();
                return;
            }

            if (!galaxyId) {
                showError();
                return;
            }

            try {
                const api = new GalaxyAPI();
                galaxyData = await api.getGalaxy(galaxyId);
                initializeGalaxy();
            } catch (error) {
                console.error('Error loading galaxy:', error);
                showError();
            }
        }

        function showError() {
            loadingScreen.style.display = 'none';
            errorScreen.style.display = 'flex';
        }

        function initializeGalaxy() {
            loadingScreen.style.display = 'none';

            // Apply custom colors to CSS
            const style = document.createElement('style');
            style.textContent = `
                .text-particle.love { color: ${galaxyData.colors.love}; text-shadow: 0 0 15px ${galaxyData.colors.love}, 0 0 25px ${galaxyData.colors.love}, 0 0 35px ${galaxyData.colors.love}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.birthday { color: ${galaxyData.colors.birthday}; text-shadow: 0 0 15px ${galaxyData.colors.birthday}, 0 0 25px ${galaxyData.colors.birthday}, 0 0 35px ${galaxyData.colors.birthday}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.date { color: ${galaxyData.colors.date}; text-shadow: 0 0 20px ${galaxyData.colors.date}, 0 0 30px ${galaxyData.colors.date}, 0 0 40px ${galaxyData.colors.date}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.special { color: ${galaxyData.colors.special}; text-shadow: 0 0 15px ${galaxyData.colors.special}, 0 0 25px ${galaxyData.colors.special}, 0 0 35px ${galaxyData.colors.special}, 2px 2px 6px rgba(0,0,0,0.9); }
                .text-particle.heart { color: ${galaxyData.colors.heart}; text-shadow: 0 0 20px ${galaxyData.colors.heart}, 0 0 30px ${galaxyData.colors.heart}, 0 0 40px ${galaxyData.colors.heart}, 3px 3px 8px rgba(0,0,0,0.9); }
            `;
            document.head.appendChild(style);

            // Phát nhạc nếu có
    if (galaxyData.song) {
        const audio = document.getElementById('galaxyAudio');

        // Set source
        if (galaxyData.song.startsWith('http')) {
            audio.src = galaxyData.song;
        } else {
            audio.src = `songs/${galaxyData.song}`;
        }

        // Cấu hình audio để lặp lại
        audio.loop = true;
        audio.volume = 0.7; // Giảm âm lượng một chút

        // Hàm phát nhạc
        const playAudio = () => {
            audio.play().catch(error => {
                console.log('Không thể tự động phát nhạc:', error);
            });
        };

        // Thử phát ngay lập tức (có thể bị chặn)
        playAudio();

        // Phát khi user tương tác lần đầu
        const playAudioOnUserGesture = (event) => {
            playAudio();
            // Chỉ remove listener sau khi đã phát thành công
            audio.addEventListener('play', () => {
                document.removeEventListener('touchstart', playAudioOnUserGesture);
                document.removeEventListener('click', playAudioOnUserGesture);
                document.removeEventListener('keydown', playAudioOnUserGesture);
                document.removeEventListener('mousemove', playAudioOnUserGesture);
            }, { once: true });
        };

        // Lắng nghe nhiều loại sự kiện để đảm bảo phát được
        document.addEventListener('touchstart', playAudioOnUserGesture);
        document.addEventListener('click', playAudioOnUserGesture);
        document.addEventListener('keydown', playAudioOnUserGesture);
        document.addEventListener('mousemove', playAudioOnUserGesture);

        // Thử phát lại nếu bị dừng bất ngờ
        audio.addEventListener('ended', () => {
            if (audio.loop) {
                setTimeout(() => {
                    playAudio();
                }, 100);
            }
        });

        // Xử lý lỗi
        audio.addEventListener('error', (e) => {
            console.error('Lỗi phát nhạc:', e);
        });

        // Hiển thị nút điều khiển âm thanh
        const audioControl = document.getElementById('audioControl');
        audioControl.style.display = 'flex';

        // Xử lý click nút điều khiển
        audioControl.addEventListener('click', () => {
            if (audio.paused) {
                playAudio();
                audioControl.textContent = '🔊';
                audioControl.classList.remove('muted');
                audioControl.title = 'Tắt nhạc';
            } else {
                audio.pause();
                audioControl.textContent = '🔇';
                audioControl.classList.add('muted');
                audioControl.title = 'Bật nhạc';
            }
        });

        // Cập nhật icon khi audio thay đổi trạng thái
        audio.addEventListener('play', () => {
            audioControl.textContent = '🔊';
            audioControl.classList.remove('muted');
            audioControl.title = 'Tắt nhạc';
        });

        audio.addEventListener('pause', () => {
            audioControl.textContent = '🔇';
            audioControl.classList.add('muted');
            audioControl.title = 'Bật nhạc';
        });
    }

            createStars();
            startParticleAnimation();

            // Hiển thị hướng dẫn ban đầu
            setTimeout(() => {
                showSpeedNotification(false);
            }, 2000);
        }

        function getRandomMessage() {
            return galaxyData.messages[Math.floor(Math.random() * galaxyData.messages.length)];
        }

        function getRandomIcon() {
            return galaxyData.icons[Math.floor(Math.random() * galaxyData.icons.length)];
        }

        function getMessageClass(message) {
            const lowerMessage = message.toLowerCase();
            if (lowerMessage.includes("love") || lowerMessage.includes("yêu") || lowerMessage.includes("tình")) {
                return "love";
            } else if (lowerMessage.includes("birthday") || lowerMessage.includes("sinh nhật") || lowerMessage.includes("chúc mừng")) {
                return "birthday";
            } else if (/\d/.test(message) || message.includes("/")) {
                return "date";
            } else {
                return "special";
            }
        }

        function createTextParticle() {
            if (activeParticles.size >= maxParticles) {
                return;
            }

            const isIcon = Math.random() > 0.7;
            const element = document.createElement('div');

            if (isIcon) {
                element.className = 'text-particle heart';
                element.textContent = getRandomIcon();
            } else {
                const message = getRandomMessage();
                element.className = `text-particle ${getMessageClass(message)}`;
                element.textContent = message;
            }

            const xPos = Math.random() * 100;
            const zPos = (Math.random() - 0.5) * (isMobile ? 300 : 500);
            // Responsive animation duration - chậm hơn để dễ đọc
            const baseDuration = isSmallMobile ? 4 : isMobile ? 5 : 6;
            const animationDuration = Math.random() * 2 + baseDuration;

            element.style.left = xPos + '%';

            // Responsive font sizes
            let baseFontSize, fontSizeVariation;
            if (isSmallMobile) {
                baseFontSize = isIcon ? 12 : 10;
                fontSizeVariation = isIcon ? 6 : 4;
            } else if (isMobile) {
                baseFontSize = isIcon ? 16 : 14;
                fontSizeVariation = isIcon ? 8 : 6;
            } else {
                baseFontSize = isIcon ? 22 : 18;
                fontSizeVariation = isIcon ? 10 : 8;
            }

            element.style.fontSize = (Math.random() * fontSizeVariation + baseFontSize) + 'px';

            const depthOpacity = Math.max(0.4, 1 - Math.abs(zPos) / (isMobile ? 250 : 400));
            element.style.opacity = depthOpacity;

            let startTime = null;
            let startY = -150;
            let endY = window.innerHeight + 150;
            const thisParticleSpeed = particleSpeedMultiplier; // Lưu multiplier tại thời điểm tạo

            function animateParticle(timestamp) {
                if (!startTime) startTime = timestamp;
                const progress = (timestamp - startTime) / (animationDuration * 1000 * thisParticleSpeed);

                if (progress < 1) {
                    const currentY = startY + (endY - startY) * progress;
                    const opacity = progress < 0.05 ? progress * 20 :
                                  progress > 0.95 ? (1 - progress) * 20 :
                                  depthOpacity;

                    element.style.transform = `translate3d(0, ${currentY}px, ${zPos}px)`;
                    element.style.opacity = opacity;

                    requestAnimationFrame(animateParticle);
                } else {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                        activeParticles.delete(element);
                    }
                }
            }

            galaxy.appendChild(element);
            activeParticles.add(element);
            requestAnimationFrame(animateParticle);
        }

        function createImageParticle() {
            if (!galaxyData.images || galaxyData.images.length === 0) return;
            if (activeParticles.size >= maxParticles) return;

            const imgUrl = galaxyData.images[Math.floor(Math.random() * galaxyData.images.length)];
            const element = document.createElement('img');
            element.src = imgUrl;
            element.className = 'image-particle';
            element.style.position = 'absolute';
            // Responsive image sizes
            const imageSize = isSmallMobile ? 40 : isMobile ? 50 : 80;
            element.style.width = imageSize + 'px';
            element.style.height = imageSize + 'px';
            element.style.objectFit = 'cover';
            element.style.borderRadius = '12px';
            element.style.boxShadow = '0 0 20px #fff8, 0 2px 8px #000a';
            element.style.pointerEvents = 'none';

            const xPos = Math.random() * 100;
            const zPos = (Math.random() - 0.5) * (isMobile ? 300 : 500);
            // Responsive animation duration cho ảnh - chậm hơn text
            const baseDuration = isSmallMobile ? 5 : isMobile ? 6 : 7;
            const animationDuration = Math.random() * 2 + baseDuration;

            element.style.left = xPos + '%';

            let startTime = null;
            let startY = -100;
            let endY = window.innerHeight + 100;
            const thisParticleSpeed = particleSpeedMultiplier; // Lưu multiplier tại thời điểm tạo

            function animateParticle(timestamp) {
                if (!startTime) startTime = timestamp;
                const progress = (timestamp - startTime) / (animationDuration * 1000 * thisParticleSpeed);

                if (progress < 1) {
                    const currentY = startY + (endY - startY) * progress;
                    const opacity = progress < 0.05 ? progress * 20 :
                            progress > 0.95 ? (1 - progress) * 20 : 1;
            element.style.transform = `translate3d(0, ${currentY}px, ${zPos}px)`;
            element.style.opacity = opacity;
            requestAnimationFrame(animateParticle);
                } else {
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                        activeParticles.delete(element);
                    }
                }
            }

            galaxy.appendChild(element);
            activeParticles.add(element);
            requestAnimationFrame(animateParticle);
        }

        function createStars() {
            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                const angle = Math.random() * Math.PI * 10;
                const radius = Math.random() * (isMobile ? 800 : 1500) + (isMobile ? 200 : 300);
                const spiralHeight = Math.sin(angle) * (isMobile ? 100 : 150);

                const x = Math.cos(angle) * radius;
                const y = spiralHeight + (Math.random() - 0.5) * (isMobile ? 1000 : 2000);
                const z = Math.sin(angle) * radius * 0.5;

                star.style.left = `calc(50% + ${x}px)`;
                star.style.top = `calc(50% + ${y}px)`;
                star.style.transform = `translateZ(${z}px)`;
                star.style.animationDelay = Math.random() * 3 + 's';

                const depthBrightness = Math.max(0.1, 1 - Math.abs(z) / (isMobile ? 800 : 1200));
                star.style.opacity = depthBrightness;

                galaxy.appendChild(star);
            }
        }

        function updateGalaxyTransform() {
            requestAnimationFrame(() => {
                galaxy.style.transform = `translate(-50%, -50%) rotateX(${rotationX}deg) rotateY(${rotationY}deg) scale(${scale})`;
            });
        }

        function startParticleAnimation() {
            setInterval(() => {
        // 30% xác suất tạo ảnh nếu có ảnh, còn lại là icon/chữ
        if (galaxyData.images && galaxyData.images.length > 0 && Math.random() < 0.3) {
            createImageParticle();
        } else {
            createTextParticle();
        }
    }, particleInterval);

    const initialParticles = isMobile ? 25 : 20;
    for (let i = 0; i < initialParticles; i++) {
        setTimeout(() => {
            if (galaxyData.images && galaxyData.images.length > 0 && Math.random() < 0.3) {
                createImageParticle();
            } else {
                createTextParticle();
            }
        }, i * (particleInterval * 0.6));
    }
        }

        // Mouse events for desktop
        document.addEventListener('mousedown', (e) => {
            isDragging = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const deltaX = e.clientX - lastMouseX;
                const deltaY = e.clientY - lastMouseY;

                const sensitivity = isMobile ? 0.3 : 0.5;
                rotationY += deltaX * sensitivity;
                rotationX -= deltaY * sensitivity;

                updateGalaxyTransform();

                lastMouseX = e.clientX;
                lastMouseY = e.clientY;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Enhanced touch events for mobile
        let initialDistance = 0;
        let initialScale = 1;

        document.addEventListener('touchstart', (e) => {
            e.preventDefault();

            if (e.touches.length === 1) {
                isDragging = true;
                lastMouseX = e.touches[0].clientX;
                lastMouseY = e.touches[0].clientY;
            } else if (e.touches.length === 2) {
                isDragging = false;
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                initialDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );
                initialScale = scale;
            }
        }, { passive: false });

        document.addEventListener('touchmove', (e) => {
            e.preventDefault();

            if (e.touches.length === 1 && isDragging) {
                const deltaX = e.touches[0].clientX - lastMouseX;
                const deltaY = e.touches[0].clientY - lastMouseY;

                const sensitivity = 0.3;
                rotationY += deltaX * sensitivity;
                rotationX -= deltaY * sensitivity;

                updateGalaxyTransform();

                lastMouseX = e.touches[0].clientX;
                lastMouseY = e.touches[0].clientY;
            } else if (e.touches.length === 2) {
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                );

                const scaleChange = currentDistance / initialDistance;
                scale = Math.max(0.5, Math.min(2, initialScale * scaleChange));

                updateGalaxyTransform();
            }
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            e.preventDefault();
            isDragging = false;
        }, { passive: false });

        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                location.reload();
            }, 100);
        });

        // Initialize
        loadGalaxyData();
    </script>
</body>
</html>
